// Mock AI/ML Assessment Service
export interface AssessmentInput {
  roofArea: number
  openSpace: number
  dwellers: number
  latitude: number
  longitude: number
}

export interface AssessmentResult {
  feasibilityScore: number
  feasibilityStatus: 'High' | 'Medium' | 'Low'
  estimatedHarvest: number
  recommendedStructure: string
  dimensions: {
    pit: { length: number; width: number; depth: number }
    trench: { length: number; width: number; depth: number }
  }
  costEstimate: number
  benefits: {
    annualSaving: number
    paybackPeriod: number
    environmentalImpact: string
  }
}

// Mock rainfall data (in mm/year) based on location
const getRainfallData = (lat: number, lng: number): number => {
  // Simulate different rainfall zones in India
  if (lat > 25) return 800 + Math.random() * 400 // Northern states
  if (lat < 15) return 1200 + Math.random() * 800 // Southern states
  return 1000 + Math.random() * 600 // Central states
}

// Mock soil type determination
const getSoilType = (lat: number, lng: number): string => {
  const soilTypes = ['Clay', 'Sandy', 'Loam', 'Rocky']
  return soilTypes[Math.floor(Math.random() * soilTypes.length)]
}

// Mock ML model for assessment
export const performAssessment = async (input: AssessmentInput): Promise<AssessmentResult> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 2000))

  const rainfall = getRainfallData(input.latitude, input.longitude)
  const soilType = getSoilType(input.latitude, input.longitude)
  
  // Calculate feasibility score (0-100)
  let score = 50
  score += Math.min(input.roofArea / 100 * 20, 25) // Roof area factor
  score += Math.min(input.openSpace / 50 * 15, 20) // Open space factor
  score += Math.min(rainfall / 1500 * 20, 25) // Rainfall factor
  score += soilType === 'Sandy' ? 10 : soilType === 'Clay' ? -5 : 5 // Soil factor
  
  const feasibilityScore = Math.min(Math.max(score, 0), 100)
  
  let feasibilityStatus: 'High' | 'Medium' | 'Low'
  if (feasibilityScore >= 70) feasibilityStatus = 'High'
  else if (feasibilityScore >= 40) feasibilityStatus = 'Medium'
  else feasibilityStatus = 'Low'

  // Calculate estimated harvest (litres/year)
  const runoffCoeff = 0.8 // Typical for concrete roofs
  const estimatedHarvest = (input.roofArea * rainfall * runoffCoeff)

  // Recommend structure based on space and feasibility
  let recommendedStructure = 'Surface Tank'
  if (input.openSpace > 30 && feasibilityScore > 60) {
    recommendedStructure = 'Recharge Pit with Storage Tank'
  } else if (input.openSpace > 15) {
    recommendedStructure = 'Recharge Trench'
  }

  // Calculate dimensions
  const storageCapacity = Math.min(estimatedHarvest * 0.3, input.openSpace * 1000)
  const pitLength = Math.sqrt(storageCapacity / 1000) * 2
  const pitWidth = pitLength * 0.8
  const pitDepth = 3

  const costEstimate = 
    input.roofArea * 150 + // Guttering and pipes
    storageCapacity * 0.8 + // Storage cost
    (recommendedStructure.includes('Pit') ? 25000 : 15000) // Structure cost

  return {
    feasibilityScore: Math.round(feasibilityScore),
    feasibilityStatus,
    estimatedHarvest: Math.round(estimatedHarvest),
    recommendedStructure,
    dimensions: {
      pit: {
        length: Math.round(pitLength * 10) / 10,
        width: Math.round(pitWidth * 10) / 10,
        depth: pitDepth
      },
      trench: {
        length: Math.round(pitLength * 1.5 * 10) / 10,
        width: 1.0,
        depth: 2.0
      }
    },
    costEstimate: Math.round(costEstimate),
    benefits: {
      annualSaving: Math.round(estimatedHarvest * 0.002), // ₹2 per litre
      paybackPeriod: Math.round(costEstimate / (estimatedHarvest * 0.002)),
      environmentalImpact: `Saves ${Math.round(estimatedHarvest / 1000)} cubic meters of groundwater annually`
    }
  }
}