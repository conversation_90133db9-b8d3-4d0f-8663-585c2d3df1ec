import { createClient } from '@supabase/supabase-js'

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'your-supabase-url'
const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY || 'your-supabase-anon-key'

export const supabase = createClient(supabaseUrl, supabaseKey)

export type User = {
  id: string
  email: string
  name?: string
  location?: string
  household_size?: number
  created_at: string
}

export type Assessment = {
  id: string
  user_id: string
  roof_area: number
  open_space: number
  dwellers: number
  latitude: number
  longitude: number
  feasibility_score: number
  feasibility_status: 'High' | 'Medium' | 'Low'
  estimated_harvest: number
  recommended_structure: string
  cost_estimate: number
  created_at: string
}