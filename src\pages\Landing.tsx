import { motion } from 'framer-motion'
import { <PERSON> } from 'react-router-dom'
import { ArrowRight, Droplets, MapPin, Calculator, FileText, CheckCircle, Star, Users, Award, Globe } from 'lucide-react'
import { Button } from '../components/ui/button'
import { Card, CardContent } from '../components/ui/card'

const features = [
  {
    icon: <MapPin className="w-12 h-12 text-blue-500" />,
    title: 'Location Intelligence',
    description: 'Advanced GIS integration for precise location-based analysis and recommendations.'
  },
  {
    icon: <Calculator className="w-12 h-12 text-green-500" />,
    title: 'AI-Powered Assessment',
    description: 'Machine learning algorithms analyze your site data for optimal harvesting potential.'
  },
  {
    icon: <FileText className="w-12 h-12 text-orange-500" />,
    title: 'Detailed Reports',
    description: 'Comprehensive PDF reports with CGWB guidelines and implementation roadmap.'
  },
  {
    icon: <CheckCircle className="w-12 h-12 text-purple-500" />,
    title: 'Feasibility Analysis',
    description: 'Complete cost-benefit analysis with payback period and environmental impact.'
  }
]

const stats = [
  { icon: <Users className="w-8 h-8" />, value: '10,000+', label: 'Assessments Completed' },
  { icon: <Droplets className="w-8 h-8" />, value: '5M+', label: 'Litres Water Saved' },
  { icon: <Award className="w-8 h-8" />, value: '98%', label: 'Success Rate' },
  { icon: <Globe className="w-8 h-8" />, value: '28', label: 'States Covered' }
]

const testimonials = [
  {
    name: 'Priya Sharma',
    role: 'Homeowner, Mumbai',
    content: 'The assessment was incredibly detailed. We saved 40% on our water bills within the first year!',
    rating: 5
  },
  {
    name: 'Raj Patel',
    role: 'Architect, Bangalore',
    content: 'I use this tool for all my projects. The AI recommendations are spot-on and save me hours of calculation.',
    rating: 5
  },
  {
    name: 'Dr. Sarah Wilson',
    role: 'Environmental Consultant',
    content: 'The most comprehensive RTRWH assessment tool available. Highly recommend for professionals.',
    rating: 5
  }
]

export default function Landing() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative pt-20 pb-20 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-white to-green-50"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-left"
            >
              <h1 className="text-5xl lg:text-7xl font-bold text-gray-900 leading-tight mb-6">
                Smart Rainwater
                <span className="text-blue-600 block">Harvesting Made Simple</span>
              </h1>
              <p className="text-xl text-gray-600 mb-8 leading-relaxed max-w-lg">
                Get instant AI-powered assessments for rooftop rainwater harvesting potential with detailed feasibility reports and implementation guidelines.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link to="/signup">
                  <Button size="lg" className="text-lg h-14 px-8 bg-blue-600 hover:bg-blue-700">
                    Check My Potential
                    <ArrowRight className="w-5 h-5 ml-2" />
                  </Button>
                </Link>
                <Link to="/demo">
                  <Button variant="outline" size="lg" className="text-lg h-14 px-8 border-2">
                    View Demo
                  </Button>
                </Link>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="relative"
            >
              <div className="bg-gradient-to-br from-blue-500 to-green-500 rounded-3xl p-8 shadow-2xl">
                <div className="bg-white rounded-2xl p-6 space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600 font-medium">Assessment Result</span>
                    <div className="flex items-center space-x-1">
                      <Star className="w-4 h-4 text-yellow-500 fill-currentColor" />
                      <span className="text-sm text-gray-600">High Feasibility</span>
                    </div>
                  </div>
                  <div className="text-3xl font-bold text-green-600">85/100</div>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Annual Harvest</span>
                      <span className="font-semibold">12,500L</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Cost Savings</span>
                      <span className="font-semibold">₹25,000/year</span>
                    </div>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-3">
                    <div className="bg-gradient-to-r from-blue-500 to-green-500 h-3 rounded-full" style={{ width: '85%' }}></div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1 }}
                className="text-center"
              >
                <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-2xl mb-4 text-blue-600">
                  {stat.icon}
                </div>
                <div className="text-3xl font-bold text-gray-900 mb-2">{stat.value}</div>
                <div className="text-gray-600">{stat.label}</div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <motion.h2
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6"
            >
              Powerful Features for
              <span className="text-blue-600 block">Smart Water Management</span>
            </motion.h2>
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.1 }}
              className="text-xl text-gray-600 max-w-3xl mx-auto"
            >
              Our comprehensive platform combines cutting-edge technology with expert knowledge to deliver accurate rainwater harvesting assessments.
            </motion.p>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ scale: 1.02 }}
              >
                <Card className="h-full p-8 hover:shadow-lg transition-all duration-300">
                  <CardContent className="p-0">
                    <div className="mb-6">{feature.icon}</div>
                    <h3 className="text-2xl font-bold text-gray-900 mb-4">{feature.title}</h3>
                    <p className="text-gray-600 text-lg leading-relaxed">{feature.description}</p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <motion.h2
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6"
            >
              How It Works
            </motion.h2>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {[
              {
                step: '01',
                title: 'Input Your Details',
                description: 'Provide basic information about your roof area, location, and household requirements.'
              },
              {
                step: '02',
                title: 'AI Analysis',
                description: 'Our advanced algorithms analyze rainfall patterns, soil conditions, and feasibility factors.'
              },
              {
                step: '03',
                title: 'Get Your Report',
                description: 'Receive a comprehensive assessment with recommendations and implementation guidelines.'
              }
            ].map((item, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.2 }}
                className="text-center"
              >
                <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full text-white font-bold text-2xl mb-6">
                  {item.step}
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-4">{item.title}</h3>
                <p className="text-gray-600 text-lg leading-relaxed">{item.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <motion.h2
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6"
            >
              What Our Users Say
            </motion.h2>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1 }}
              >
                <Card className="h-full p-8">
                  <CardContent className="p-0">
                    <div className="flex mb-4">
                      {[...Array(testimonial.rating)].map((_, i) => (
                        <Star key={i} className="w-5 h-5 text-yellow-500 fill-currentColor" />
                      ))}
                    </div>
                    <p className="text-gray-600 text-lg leading-relaxed mb-6 italic">
                      "{testimonial.content}"
                    </p>
                    <div>
                      <div className="font-semibold text-gray-900">{testimonial.name}</div>
                      <div className="text-sm text-gray-500">{testimonial.role}</div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-blue-600 to-blue-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
          >
            <h2 className="text-4xl lg:text-5xl font-bold text-white mb-6">
              Ready to Start Saving Water?
            </h2>
            <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
              Join thousands of users who are already making a positive impact with smart rainwater harvesting solutions.
            </p>
            <Link to="/signup">
              <Button size="lg" variant="secondary" className="text-lg h-14 px-8 bg-white text-blue-600 hover:bg-gray-50">
                Get Started Today
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
            </Link>
          </motion.div>
        </div>
      </section>
    </div>
  )
}