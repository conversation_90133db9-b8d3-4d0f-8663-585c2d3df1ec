import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, useMapEvents } from 'react-leaflet'
import { 
  Home, 
  User, 
  Calculator, 
  FileText, 
  MapPin, 
  Droplets,
  TrendingUp,
  Download,
  Settings
} from 'lucide-react'
import { Button } from '../components/ui/button'
import { Input } from '../components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card'
import { useAuth } from '../hooks/useAuth'
import { performAssessment, type AssessmentResult } from '../services/assessmentService'
import { generateAssessmentReport } from '../services/pdfService'
import 'leaflet/dist/leaflet.css'

const assessmentSchema = z.object({
  roofArea: z.number().min(10, 'Roof area must be at least 10 sq meters'),
  openSpace: z.number().min(5, 'Open space must be at least 5 sq meters'),
  dwellers: z.number().min(1, 'Number of dwellers must be at least 1'),
  latitude: z.number().min(-90).max(90),
  longitude: z.number().min(-180).max(180)
})

type AssessmentForm = z.infer<typeof assessmentSchema>

// Map click handler component
function LocationSelector({ onLocationSelect }: { onLocationSelect: (lat: number, lng: number) => void }) {
  useMapEvents({
    click: (e) => {
      onLocationSelect(e.latlng.lat, e.latlng.lng)
    }
  })
  return null
}

export default function Dashboard() {
  const [activeTab, setActiveTab] = useState('profile')
  const [isAssessing, setIsAssessing] = useState(false)
  const [assessmentResult, setAssessmentResult] = useState<AssessmentResult | null>(null)
  const [selectedLocation, setSelectedLocation] = useState<[number, number]>([28.6139, 77.2090]) // Delhi
  const { user, signOut } = useAuth()

  const {
    register,
    handleSubmit,
    setValue,
    formState: { errors }
  } = useForm<AssessmentForm>({
    resolver: zodResolver(assessmentSchema),
    defaultValues: {
      roofArea: 100,
      openSpace: 50,
      dwellers: 4,
      latitude: 28.6139,
      longitude: 77.2090
    }
  })

  useEffect(() => {
    setValue('latitude', selectedLocation[0])
    setValue('longitude', selectedLocation[1])
  }, [selectedLocation, setValue])

  const onSubmit = async (data: AssessmentForm) => {
    setIsAssessing(true)
    try {
      const result = await performAssessment(data)
      setAssessmentResult(result)
      setActiveTab('results')
    } catch (error) {
      console.error('Assessment error:', error)
      alert('Assessment failed. Please try again.')
    } finally {
      setIsAssessing(false)
    }
  }

  const handleDownloadReport = async () => {
    if (assessmentResult) {
      await generateAssessmentReport(assessmentResult, {
        name: user?.name,
        email: user?.email
      })
    }
  }

  const sidebarItems = [
    { id: 'profile', label: 'Profile', icon: User },
    { id: 'assessment', label: 'New Assessment', icon: Calculator },
    { id: 'results', label: 'Results', icon: TrendingUp },
    { id: 'reports', label: 'My Reports', icon: FileText },
    { id: 'settings', label: 'Settings', icon: Settings }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-2">
              <div className="p-2 bg-blue-500 rounded-xl">
                <Droplets className="w-6 h-6 text-white" />
              </div>
              <span className="text-xl font-bold text-gray-900">RainHarvest</span>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-gray-700">Welcome, {user?.name || user?.email}</span>
              <Button variant="outline" onClick={signOut}>
                Sign Out
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="flex">
        {/* Sidebar */}
        <div className="w-64 bg-white shadow-sm h-screen sticky top-0">
          <nav className="mt-8">
            <div className="px-4 space-y-2">
              {sidebarItems.map((item) => (
                <button
                  key={item.id}
                  onClick={() => setActiveTab(item.id)}
                  className={`w-full flex items-center space-x-3 px-4 py-3 text-left rounded-xl transition-colors ${
                    activeTab === item.id
                      ? 'bg-blue-50 text-blue-700 font-medium'
                      : 'text-gray-700 hover:bg-gray-100'
                  }`}
                >
                  <item.icon className="w-5 h-5" />
                  <span>{item.label}</span>
                </button>
              ))}
            </div>
          </nav>
        </div>

        {/* Main Content */}
        <div className="flex-1 p-8">
          {activeTab === 'profile' && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="space-y-6"
            >
              <h1 className="text-3xl font-bold text-gray-900">Profile</h1>
              <div className="grid md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Personal Information</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Name</label>
                      <Input value={user?.name || ''} readOnly />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                      <Input value={user?.email || ''} readOnly />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Member Since</label>
                      <Input value={new Date(user?.created_at || '').toLocaleDateString()} readOnly />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Quick Stats</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Assessments Completed</span>
                      <span className="font-semibold">3</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Total Water Harvest Potential</span>
                      <span className="font-semibold">15,600L</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Estimated Annual Savings</span>
                      <span className="font-semibold">₹31,200</span>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </motion.div>
          )}

          {activeTab === 'assessment' && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="space-y-6"
            >
              <h1 className="text-3xl font-bold text-gray-900">New Assessment</h1>
              <div className="grid lg:grid-cols-2 gap-8">
                <Card>
                  <CardHeader>
                    <CardTitle>Site Information</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Roof Area (sq meters)
                        </label>
                        <Input
                          type="number"
                          {...register('roofArea', { valueAsNumber: true })}
                          className={errors.roofArea ? 'border-red-300' : ''}
                        />
                        {errors.roofArea && (
                          <p className="text-red-600 text-sm mt-1">{errors.roofArea.message}</p>
                        )}
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Open Space Available (sq meters)
                        </label>
                        <Input
                          type="number"
                          {...register('openSpace', { valueAsNumber: true })}
                          className={errors.openSpace ? 'border-red-300' : ''}
                        />
                        {errors.openSpace && (
                          <p className="text-red-600 text-sm mt-1">{errors.openSpace.message}</p>
                        )}
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Number of Dwellers
                        </label>
                        <Input
                          type="number"
                          {...register('dwellers', { valueAsNumber: true })}
                          className={errors.dwellers ? 'border-red-300' : ''}
                        />
                        {errors.dwellers && (
                          <p className="text-red-600 text-sm mt-1">{errors.dwellers.message}</p>
                        )}
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Latitude
                          </label>
                          <Input
                            type="number"
                            step="any"
                            {...register('latitude', { valueAsNumber: true })}
                            className={errors.latitude ? 'border-red-300' : ''}
                            readOnly
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Longitude
                          </label>
                          <Input
                            type="number"
                            step="any"
                            {...register('longitude', { valueAsNumber: true })}
                            className={errors.longitude ? 'border-red-300' : ''}
                            readOnly
                          />
                        </div>
                      </div>

                      <Button
                        type="submit"
                        className="w-full"
                        disabled={isAssessing}
                      >
                        {isAssessing ? 'Analyzing...' : 'Start Assessment'}
                      </Button>
                    </form>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <MapPin className="w-5 h-5" />
                      <span>Select Location</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="h-96 rounded-xl overflow-hidden">
                      <MapContainer
                        center={selectedLocation}
                        zoom={13}
                        style={{ height: '100%', width: '100%' }}
                      >
                        <TileLayer
                          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                        />
                        <Marker position={selectedLocation} />
                        <LocationSelector
                          onLocationSelect={(lat, lng) => setSelectedLocation([lat, lng])}
                        />
                      </MapContainer>
                    </div>
                    <p className="text-sm text-gray-600 mt-2">
                      Click on the map to select your location
                    </p>
                  </CardContent>
                </Card>
              </div>
            </motion.div>
          )}

          {activeTab === 'results' && assessmentResult && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="space-y-6"
            >
              <div className="flex justify-between items-center">
                <h1 className="text-3xl font-bold text-gray-900">Assessment Results</h1>
                <Button onClick={handleDownloadReport} className="flex items-center space-x-2">
                  <Download className="w-4 h-4" />
                  <span>Download Report</span>
                </Button>
              </div>

              <div className="grid md:grid-cols-3 gap-6 mb-8">
                <Card>
                  <CardContent className="p-6">
                    <div className="text-center">
                      <div className={`text-4xl font-bold mb-2 ${
                        assessmentResult.feasibilityStatus === 'High' ? 'text-green-600' :
                        assessmentResult.feasibilityStatus === 'Medium' ? 'text-yellow-600' : 'text-red-600'
                      }`}>
                        {assessmentResult.feasibilityScore}/100
                      </div>
                      <div className="text-gray-600">Feasibility Score</div>
                      <div className={`text-lg font-semibold mt-2 ${
                        assessmentResult.feasibilityStatus === 'High' ? 'text-green-600' :
                        assessmentResult.feasibilityStatus === 'Medium' ? 'text-yellow-600' : 'text-red-600'
                      }`}>
                        {assessmentResult.feasibilityStatus} Feasibility
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="text-center">
                      <div className="text-4xl font-bold text-blue-600 mb-2">
                        {(assessmentResult.estimatedHarvest / 1000).toFixed(1)}K
                      </div>
                      <div className="text-gray-600">Litres/Year</div>
                      <div className="text-lg font-semibold text-blue-600 mt-2">
                        Annual Harvest
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="text-center">
                      <div className="text-4xl font-bold text-green-600 mb-2">
                        ₹{(assessmentResult.benefits.annualSaving / 1000).toFixed(0)}K
                      </div>
                      <div className="text-gray-600">Per Year</div>
                      <div className="text-lg font-semibold text-green-600 mt-2">
                        Cost Savings
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div className="grid lg:grid-cols-2 gap-8">
                <Card>
                  <CardHeader>
                    <CardTitle>Recommendations</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">Recommended Structure</h4>
                      <p className="text-gray-600">{assessmentResult.recommendedStructure}</p>
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">Dimensions</h4>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Recharge Pit:</span>
                          <span>{assessmentResult.dimensions.pit.length}m × {assessmentResult.dimensions.pit.width}m × {assessmentResult.dimensions.pit.depth}m</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Recharge Trench:</span>
                          <span>{assessmentResult.dimensions.trench.length}m × {assessmentResult.dimensions.trench.width}m × {assessmentResult.dimensions.trench.depth}m</span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Financial Analysis</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Installation Cost:</span>
                      <span className="font-semibold">₹{assessmentResult.costEstimate.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Annual Savings:</span>
                      <span className="font-semibold text-green-600">₹{assessmentResult.benefits.annualSaving.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Payback Period:</span>
                      <span className="font-semibold">{assessmentResult.benefits.paybackPeriod} years</span>
                    </div>
                    <div className="pt-4 border-t">
                      <h4 className="font-semibold text-gray-900 mb-2">Environmental Impact</h4>
                      <p className="text-gray-600 text-sm">{assessmentResult.benefits.environmentalImpact}</p>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </motion.div>
          )}

          {activeTab === 'reports' && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="space-y-6"
            >
              <h1 className="text-3xl font-bold text-gray-900">My Reports</h1>
              <Card>
                <CardHeader>
                  <CardTitle>Assessment History</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-12">
                    <FileText className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-gray-600 mb-2">No reports yet</h3>
                    <p className="text-gray-500">Complete an assessment to generate your first report</p>
                    <Button 
                      className="mt-4" 
                      onClick={() => setActiveTab('assessment')}
                    >
                      Start Assessment
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )}

          {activeTab === 'settings' && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="space-y-6"
            >
              <h1 className="text-3xl font-bold text-gray-900">Settings</h1>
              <Card>
                <CardHeader>
                  <CardTitle>Account Settings</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Notification Preferences
                      </label>
                      <div className="space-y-2">
                        <label className="flex items-center">
                          <input type="checkbox" className="rounded border-gray-300" defaultChecked />
                          <span className="ml-2 text-sm text-gray-700">Email notifications</span>
                        </label>
                        <label className="flex items-center">
                          <input type="checkbox" className="rounded border-gray-300" defaultChecked />
                          <span className="ml-2 text-sm text-gray-700">Assessment reminders</span>
                        </label>
                      </div>
                    </div>
                    <div className="pt-4 border-t">
                      <Button variant="destructive">Delete Account</Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )}
        </div>
      </div>
    </div>
  )
}