/** @type {import('tailwindcss').Config} */
export default {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      colors: {
        primary: {
          DEFAULT: '#0071E3',
          50: '#E5F3FF',
          100: '#CCE7FF',
          200: '#99CFFF',
          300: '#66B7FF',
          400: '#339FFF',
          500: '#0071E3',
          600: '#005BB6',
          700: '#004589',
          800: '#002F5C',
          900: '#00192F'
        },
        secondary: {
          DEFAULT: '#30D158',
          50: '#E8F9EC',
          100: '#D1F3D9',
          200: '#A3E7B3',
          300: '#75DB8D',
          400: '#47CF67',
          500: '#30D158',
          600: '#26A746',
          700: '#1C7D34',
          800: '#125322',
          900: '#082911'
        },
        accent: {
          DEFAULT: '#FF9500',
          50: '#FFF2E5',
          100: '#FFE5CC',
          200: '#FFCB99',
          300: '#FFB166',
          400: '#FF9733',
          500: '#FF9500',
          600: '#CC7700',
          700: '#995900',
          800: '#663B00',
          900: '#331D00'
        }
      },
      fontFamily: {
        sans: ['-apple-system', 'BlinkMacSystemFont', 'San Francisco', 'Helvetica Neue', 'Arial', 'sans-serif']
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.5s ease-out',
        'scale': 'scale 0.2s ease-in-out'
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' }
        },
        slideUp: {
          '0%': { transform: 'translateY(20px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' }
        },
        scale: {
          '0%, 100%': { transform: 'scale(1)' },
          '50%': { transform: 'scale(0.95)' }
        }
      }
    }
  },
  plugins: []
}