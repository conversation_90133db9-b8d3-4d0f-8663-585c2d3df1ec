import jsPDF from 'jspdf'
import html2canvas from 'html2canvas'
import { AssessmentResult } from './assessmentService'
import { formatNumber, formatCurrency } from '../lib/utils'

export const generateAssessmentReport = async (
  assessmentData: AssessmentResult,
  userInfo: { name?: string; location?: string; email?: string }
) => {
  const pdf = new jsPDF()
  
  // Header
  pdf.setFontSize(24)
  pdf.setFont('helvetica', 'bold')
  pdf.text('RTRWH Assessment Report', 20, 30)
  
  pdf.setFontSize(12)
  pdf.setFont('helvetica', 'normal')
  pdf.text(`Generated on: ${new Date().toLocaleDateString('en-IN')}`, 20, 45)
  
  if (userInfo.name) {
    pdf.text(`Prepared for: ${userInfo.name}`, 20, 55)
  }
  
  // Executive Summary
  pdf.setFontSize(16)
  pdf.setFont('helvetica', 'bold')
  pdf.text('Executive Summary', 20, 75)
  
  pdf.setFontSize(11)
  pdf.setFont('helvetica', 'normal')
  
  const summaryY = 85
  pdf.text(`Feasibility Status: ${assessmentData.feasibilityStatus}`, 20, summaryY)
  pdf.text(`Feasibility Score: ${assessmentData.feasibilityScore}/100`, 20, summaryY + 10)
  pdf.text(`Annual Water Harvest: ${formatNumber(assessmentData.estimatedHarvest)} litres`, 20, summaryY + 20)
  pdf.text(`Recommended Structure: ${assessmentData.recommendedStructure}`, 20, summaryY + 30)
  pdf.text(`Estimated Cost: ${formatCurrency(assessmentData.costEstimate)}`, 20, summaryY + 40)
  pdf.text(`Annual Savings: ${formatCurrency(assessmentData.benefits.annualSaving)}`, 20, summaryY + 50)
  
  // Technical Specifications
  pdf.setFontSize(16)
  pdf.setFont('helvetica', 'bold')
  pdf.text('Technical Specifications', 20, 155)
  
  pdf.setFontSize(11)
  pdf.setFont('helvetica', 'normal')
  
  const techY = 165
  pdf.text('Recommended Dimensions:', 20, techY)
  pdf.text(`• Recharge Pit: ${assessmentData.dimensions.pit.length}m x ${assessmentData.dimensions.pit.width}m x ${assessmentData.dimensions.pit.depth}m`, 25, techY + 10)
  pdf.text(`• Recharge Trench: ${assessmentData.dimensions.trench.length}m x ${assessmentData.dimensions.trench.width}m x ${assessmentData.dimensions.trench.depth}m`, 25, techY + 20)
  
  // Benefits Analysis
  pdf.setFontSize(16)
  pdf.setFont('helvetica', 'bold')
  pdf.text('Benefits Analysis', 20, 205)
  
  pdf.setFontSize(11)
  pdf.setFont('helvetica', 'normal')
  
  const benefitsY = 215
  pdf.text(`• Annual Water Savings: ${formatNumber(assessmentData.estimatedHarvest)} litres`, 20, benefitsY)
  pdf.text(`• Annual Cost Savings: ${formatCurrency(assessmentData.benefits.annualSaving)}`, 20, benefitsY + 10)
  pdf.text(`• Payback Period: ${assessmentData.benefits.paybackPeriod} years`, 20, benefitsY + 20)
  pdf.text(`• Environmental Impact: ${assessmentData.benefits.environmentalImpact}`, 20, benefitsY + 30)
  
  // CGWB Guidelines (Mock)
  pdf.addPage()
  pdf.setFontSize(16)
  pdf.setFont('helvetica', 'bold')
  pdf.text('CGWB Guidelines & Recommendations', 20, 30)
  
  pdf.setFontSize(11)
  pdf.setFont('helvetica', 'normal')
  
  const guidelines = [
    '• Rainwater harvesting structures should be designed based on roof area and annual rainfall',
    '• Recharge structures should be located at least 3m away from building foundations',
    '• First flush diverters should be installed to ensure water quality',
    '• Regular maintenance includes cleaning of gutters and storage tanks',
    '• Overflow arrangements should be provided for excess water disposal',
    '• Water quality testing should be done periodically for potable use',
    '• Local building codes and regulations should be followed during construction',
    '• Professional consultation is recommended for complex installations'
  ]
  
  let guidelineY = 50
  guidelines.forEach(guideline => {
    pdf.text(guideline, 20, guidelineY)
    guidelineY += 12
  })
  
  // Footer
  pdf.setFontSize(8)
  pdf.setFont('helvetica', 'italic')
  pdf.text('This report is generated based on preliminary assessment. Detailed site survey is recommended.', 20, 280)
  
  // Save the PDF
  pdf.save(`RTRWH_Assessment_Report_${new Date().getTime()}.pdf`)
}